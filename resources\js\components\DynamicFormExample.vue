<template>
    <div class="container mt-4">
        <h2>Dynamic Form Fields Example</h2>
        <div class="card">
            <div class="card-body">
                <Form @submit="handleSubmit" :validation-schema="validationSchema">
                    <!-- Static fields -->
                    <div class="mb-3">
                        <label class="form-label">Job Name</label>
                        <Field name="jobName" v-model="formData.jobName" class="form-control" />
                        <ErrorMessage name="jobName" class="text-danger" />
                    </div>

                    <!-- Dynamic Form Fields Component -->
                    <DynamicFormFields
                        :sortedFormFields="sortedFormFields"
                        :formData="dynamicFormState.formData"
                        :formattedFormulaResults="formattedFormulaResults"
                        :selectOptionDepartments="dynamicFormState.selectOptionDepartments"
                        :subColumnTableDescription="dynamicFormState.subColumnTableDescription"
                        :subColumnTableOptionSelected="dynamicFormState.subColumnTableOptionSelected"
                        :maxFiles="dynamicFormState.maxFiles"
                        :maxFileSize="dynamicFormState.maxFileSize"
                        :acceptedFileTypes="dynamicFormState.acceptedFileTypes"
                        :debouncedGetOptionUsers="debouncedGetOptionUsers"
                        :debouncedGetOptionColumnData="debouncedGetOptionColumnData"
                        :updateFiles="updateFiles"
                        :updateFileChildrens="updateFileChildrens"
                        :showSubColumnTable="showSubColumnTable"
                        :converFormFields="converFormFields"
                        :formattedFormulaChildrenResults="formattedFormulaChildrenResults"
                        :addItem="addItem"
                        :removeItem="removeItem"
                    />

                    <div class="mt-3">
                        <button type="submit" class="btn btn-primary">Submit</button>
                        <button type="button" class="btn btn-secondary ms-2" @click="resetForm">Reset</button>
                    </div>
                </Form>
            </div>
        </div>

        <!-- Debug Info -->
        <div class="card mt-4">
            <div class="card-header">
                <h5>Debug Information</h5>
            </div>
            <div class="card-body">
                <h6>Form Data:</h6>
                <pre>{{ JSON.stringify(dynamicFormState.formData, null, 2) }}</pre>
                
                <h6>Static Form Data:</h6>
                <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
                
                <h6>Sorted Form Fields:</h6>
                <pre>{{ JSON.stringify(sortedFormFields, null, 2) }}</pre>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, reactive, onMounted } from 'vue'
import { Form, Field, ErrorMessage } from 'vee-validate'
import * as yup from 'yup'
import DynamicFormFields from '@/components/DynamicFormFields.vue'
import useDynamicForm from '@/composables/useDynamicForm'

export default defineComponent({
    name: "DynamicFormExample",
    
    components: {
        Form,
        Field,
        ErrorMessage,
        DynamicFormFields
    },
    
    setup() {
        // Use dynamic form composable
        const {
            state: dynamicFormState,
            sortedFormFields,
            converFormFields,
            debouncedGetOptionUsers,
            getOptionDepartments,
            formattedFormulaResults,
            formattedFormulaChildrenResults,
            updateFiles,
            updateFileChildrens,
            addItem,
            removeItem,
            createValidationSchema
        } = useDynamicForm()

        // Static form data
        const formData = reactive({
            jobName: ''
        })

        // Mock data for testing
        const mockFormFields = [
            {
                id: 1,
                type: 'VARCHAR',
                display_name: 'Text Field',
                display_name_en: 'Text Field',
                placeholder: 'Enter text',
                placeholder_en: 'Enter text',
                keyword: 'text_field',
                required: 1,
                column_width: 6,
                parent_id: null,
                order: 1,
                children: []
            },
            {
                id: 2,
                type: 'SELECT',
                display_name: 'Select Field',
                display_name_en: 'Select Field',
                placeholder: 'Choose option',
                placeholder_en: 'Choose option',
                keyword: 'select_field',
                required: 1,
                multiple: 0,
                column_width: 6,
                parent_id: null,
                order: 2,
                options: [
                    { value: 'option1', label: 'Option 1' },
                    { value: 'option2', label: 'Option 2' },
                    { value: 'option3', label: 'Option 3' }
                ],
                children: []
            }
        ]

        // Validation schema
        const validationSchema = yup.object({
            jobName: yup.string().required('Job name is required')
        })

        // Mock functions
        const debouncedGetOptionColumnData = async (query: string, field: any) => {
            return []
        }

        const showSubColumnTable = (optionSelected: any, keyName: string) => {
            console.log('showSubColumnTable called', optionSelected, keyName)
        }

        const handleSubmit = (values: any) => {
            console.log('Form submitted:', values)
            console.log('Dynamic form data:', dynamicFormState.formData)
        }

        const resetForm = () => {
            formData.jobName = ''
            dynamicFormState.formData = {}
        }

        onMounted(async () => {
            // Set mock data
            dynamicFormState.dataFormFields = mockFormFields
            await getOptionDepartments()
        })

        return {
            formData,
            dynamicFormState,
            sortedFormFields,
            converFormFields,
            debouncedGetOptionUsers,
            debouncedGetOptionColumnData,
            formattedFormulaResults,
            formattedFormulaChildrenResults,
            updateFiles,
            updateFileChildrens,
            addItem,
            removeItem,
            showSubColumnTable,
            validationSchema,
            handleSubmit,
            resetForm
        }
    }
})
</script>

<style scoped>
.container {
    max-width: 1200px;
}

pre {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    max-height: 300px;
    overflow-y: auto;
}
</style>
