import { reactive, computed, ref } from 'vue'
import { useI18n } from "vue-i18n"
import * as yup from 'yup'
import { evaluate } from 'mathjs'
import { numberCommas } from "@/utils/utils"
import { WORKFLOWS } from "@/constants/constants"
import useOptions from '@/composables/option'
import debounce from 'lodash.debounce'

export default function useDynamicForm() {
    const { t } = useI18n()
    const { getUsers, getDepartments, getScopes } = useOptions()

    // State for dynamic form
    const state = reactive({
        maxFiles: 50,
        maxFileSize: '5MB',
        acceptedFileTypes: [
            'image/*',
            'application/pdf', 
            'application/msword', 
            'application/vnd.ms-excel', 
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/plain' 
        ],
        formData: {} as any,
        itemChildrens: [] as Array<any>,
        dataFormFields: [] as Array<any>,
        selectOptionDepartments: [] as Array<any>,
        subColumnTableDescription: {} as { [key: string]: any },
        subColumnTableOptionSelected: {} as { [key: string]: any },
        subColumnTableDescriptionChildren: {} as { [key: string]: { [index: number]: any } },
        subColumnTableOptionSelectedChildren: {} as { [key: string]: { [index: number]: any } },
        selectOptionSystemDefaults: [
            { label: `${t('workflow.option_system_default.create_by')}`, description: `${t('workflow.option_system_default.create_by_desc')}`, value: WORKFLOWS.OPTION_SYSTEM_DEFAULT.CREATE_BY_ID },
        ] as Array<any>,
    })

    // Utility functions
    const transformObject = (obj: object) => {
        return Object.fromEntries(
            Object.entries(obj).map(([key, value]) => {
                if (value === 1) return [key, true];
                if (value === 0) return [key, false];
                if (typeof value === 'string' && isJsonArrayOrObject(value)) {
                    return [key, JSON.parse(value)];
                }
                if (typeof value === 'string') {
                    return [key, value];
                }
                return [key, value];
            })
        );
    };

    const isJsonArrayOrObject = (str: any) => {
        try {
            const parsed = JSON.parse(str);
            return typeof parsed === 'object' && parsed !== null;
        } catch (e) {
            return false;
        }
    };

    const mappedTypeDataField = (typeField: string) => {
        switch (typeField) {
            case 'VARCHAR':
                return 'text';
            case 'TEXT':
                return 'textarea';
            case 'INTEGER':
                return 'number';
            case 'FLOAT':
                return 'number';
            case 'DATE':
                return 'date';
            case 'TIME':
                return 'time';
            case 'SELECT':
                return 'MULTISELECT';
            case 'RADIO':
                return 'radio';
            case 'CHECKLIST':
                return 'checkbox';
            default:
                return typeField;
        }
    }

    // Initialize item for table fields
    const initializeItem = (childrens: any[]) => {
        const item = {} as any;
        childrens.forEach((child: any) => {
            const childTransformed = transformObject(child);
            if (childTransformed.type === 'FILEUPLOAD') {
                item[childTransformed.keyword] = [];
            } else if (childTransformed.type === 'MULTISELECT' || childTransformed.type === 'USER' || childTransformed.type === 'DEPARTMENT') {
                item[childTransformed.keyword] = childTransformed.multiple ? [] : null;
            } else if (childTransformed.type === 'OBJECTSYSTEM') {
                item[childTransformed.keyword] = childTransformed.multiple ? [] : null;
            } else if (childTransformed.type === 'FORMULA') {
                item[childTransformed.keyword] = '';
                item.original_formula = childTransformed.value;
                item.keyword_formula = childTransformed.keyword;
            } else {
                item[childTransformed.keyword] = childTransformed.default_value || '';
            }
        });
        return item;
    };

    // Convert form fields
    const converFormFields = (dataFormFields: any, fieldTable: boolean) => {
        if (dataFormFields.length > 0) {
            const sortedDataFields = dataFormFields.filter((item: any) => fieldTable ? item.parent_id !== null : item.parent_id === null).slice().sort((a: any, b: any) => a.order - b.order);
            const transformedData = sortedDataFields.map((dataField: any) => {
                const currentLanguage: string = localStorage.getItem('app_language') || 'vi';
                const labelKey = currentLanguage === 'vi' ? 'display_name' : 'display_name_en';
                const placeholderKey = currentLanguage === 'vi' ? 'placeholder' : 'placeholder_en';
                const item = transformObject(dataField);
                
                let validation = item.required ? 'required' : '';
                let validationMessages = {} as any;

                const formField = {
                    type: mappedTypeDataField(item.type),
                    label: item[labelKey],
                    placeholder: item[placeholderKey],
                    floatingLabel: 'true',
                    class: 'form-control',
                    name: item.keyword,
                    validation: validation,
                    validationMessages: validationMessages,
                    column_width: item.column_width,
                } as any;
                
                if (item.required) {
                    if (item.type === 'RADIO' || item.type === 'CHECKLIST') {
                        formField.legendClass = 'required-label';
                    } else {
                        formField.labelClass = 'required-label';
                    }
                    validationMessages.required = `${item[labelKey]} ${t('validate_field.display_name.required')}`;
                }

                if (item.default_value !== null) {
                    formField.value = item.default_value;
                }

                if (item.not_edit) {
                    formField.disabled = item.not_edit;
                }

                if (item.type === 'RADIO' || item.type === 'CHECKLIST' || item.type === 'DATE' || item.type === 'TIME') {
                    formField.floatingLabel = 'false';
                }

                // Add length validation for text fields
                if ((item.type === 'VARCHAR' || item.type === 'TEXT') && item.min_equal != null && item.max_equal != null) {
                    const min = parseInt(item.min_equal, 10);
                    const max = parseInt(item.max_equal, 10);
                    
                    if (!isNaN(min) && !isNaN(max)) {
                        validation += (validation ? '|' : '') + `length:${min},${max}`;
                        validationMessages.length = `${item[labelKey]} ${t('validate_field.display_name.must_between_text')} ${min} ${t('validate_field.display_name.to')} ${max} ${t('validate_field.display_name.characters')}`;
                    }
                }

                // Add between validation for number fields
                if (item.type === 'INTEGER' && item.min_equal != null && item.max_equal != null) {
                    const min = parseInt(item.min_equal, 10);
                    const max = parseInt(item.max_equal, 10);

                    if (!isNaN(min) && !isNaN(max)) {
                        validation += (validation ? '|' : '') + `between:${min},${max}`;
                        validationMessages.between = `${item[labelKey]} ${t('validate_field.display_name.must_between_number')} ${min} ${t('validate_field.display_name.to')} ${max}`;
                    }
                }

                if (validation) {
                    formField.validation = validation;
                }

                // Remove empty properties
                Object.keys(formField).forEach(key => {
                    if (!formField[key] && formField[key] !== 0) {
                        delete formField[key];
                    }
                });
                
                // Add options for radio and checklist
                if ((item.type === 'RADIO' || item.type === 'CHECKLIST') && item.options) {
                    formField.options = item.options;
                }

                // Add options and multiple for select
                if (item.type === 'SELECT') {
                    formField.options = item.options;
                    formField.multiple = item.multiple;
                    formField.disabled = item.not_edit;
                }

                if (item.type === 'USER' || item.type === 'DEPARTMENT' || item.type === 'FILEUPLOAD' || item.type === 'OBJECTSYSTEM') {
                    formField.multiple = item.multiple;
                    formField.disabled = item.not_edit;
                }

                if (item.parent_id !== null) {
                    formField.parent_id = item.parent_id;
                }

                if (item.children.length > 0) {
                    formField.childrens = item.children.map((child: any) => {
                        return transformObject(child);
                    });
                }

                if (item.type === 'OBJECTSYSTEM') {
                    if (item.column_table !== null) {
                        formField.column_table = item.column_table;
                    }
                    if (item.object_table !== null) {
                        formField.object_table = item.object_table;
                    }
                    if (item.sub_column_table !== null) {
                        formField.sub_column_table = item.sub_column_table;
                    }
                }
                
                return formField;
            });

            transformedData.forEach((field: any) => {
                if (field.type === 'TABLE' && field.childrens) {
                    state.itemChildrens[field.name] = [{ ...initializeItem(field.childrens) }];
                }
            });
            
            return transformedData;
        } else {
            return [];
        }
    }

    // Computed for sorted form fields
    const sortedFormFields = computed(() => {
        return converFormFields(state.dataFormFields, false);
    });

    // Option functions
    const getOptionUsers = async (query: string) => {
        let result = await getUsers(query);
        if (Array.isArray(result) && result.length > 0) {
            return result.map((elem: any) => (
                {
                    value: elem.id,
                    label: `${elem.account_name} - ${elem.full_name}`,
                }
            ));
        }
    }

    const debouncedGetOptionUsers = debounce(getOptionUsers, 500);

    const getOptionDepartments = async () => {
        let result = await getDepartments();
        if (Array.isArray(result) && result.length > 0) {
            state.selectOptionDepartments = result.map((elem: any) => (
                {
                    value: elem.id,
                    label: elem.name,
                    type: elem.type,
                }
            ));
        }
    }

    const getOptionProcessScopes = async (query: string) => {
        let result = await getScopes(query);
        if (Array.isArray(result) && result.length > 0) {
            return [...state.selectOptionSystemDefaults, ...result];
        }
        return [...state.selectOptionSystemDefaults];
    }

    const debouncedGetOptionScopes = debounce(getOptionProcessScopes, 500);

    // Formula calculation functions
    const extractSumPlus = (str: string) => {
        const regex = /\(([^)]+)\)/;
        const match = str.match(regex);

        if (match) {
            const insideParentheses = match[1];
            const [value1, value2] = insideParentheses.split('.');

            if (value1 && value2) {
                return [value1, value2];
            } else {
                return null;
            }
        }
        return null;
    };

    const removeCommas = (value: any) => {
        return value.replace(/,/g,'');
    }

    const sumColumn = (field: any) => {
        const SUMPLUS = field.value;
        const result = extractSumPlus(SUMPLUS);
        if (result) {
            const [tableName, columnName] = result;
            return state.itemChildrens[tableName].reduce((total: number, itemChildren: any) => {
                const value = parseFloat(removeCommas(itemChildren[columnName])) || 0;
                return total + value;
            }, 0);
        } else {
            console.log("Không thể bóc tách các giá trị.");
        }
    };

    const calculateFormula = (formula: string, context: any) => {
        try {
            const expression = formula.replace(/^=/, '');
            const scope = { ...context };

            Object.keys(scope).forEach(key => {
                if (typeof scope[key] === 'string' && scope[key].includes(',')) {
                    scope[key] = parseFloat(removeCommas(scope[key])) || 0;
                }
            });

            return evaluate(expression, scope);
        } catch (error) {
            console.error('Formula calculation error:', error);
            return 0;
        }
    };

    const formulaResults = computed(() => {
        const results = {};
        sortedFormFields.value.forEach((field: any) => {
            if (field.type === 'FORMULA') {
                const formula = field.value;
                if (formula.startsWith('=')) {
                    results[field.name] = calculateFormula(field.value, results);
                } else {
                    results[field.name] = sumColumn(field);
                }
            }
        });

        return results;
    });

    const formattedFormulaResults = computed(() => {
        const results = {};
        for (const key in formulaResults.value) {
            results[key] = numberCommas(formulaResults.value[key]);
            state.formData[key] = results[key];
        }

        return results;
    });

    const formattedFormulaChildrenResults = (nameKey: string) => {
        const items = state.itemChildrens[nameKey];
        if (!items) return;
        items.forEach((item: any) => {
            if (!item.original_formula == false) {
                const calculatedValue = calculateFormula(item.original_formula, item);
                item[item.keyword_formula] = numberCommas(calculatedValue);
            }
        });

        return items;
    };

    // File handling functions
    const updateFiles = (fileItemUploads: any[], fieldName: string) => {
        state.formData[fieldName] = fileItemUploads;
    };

    const updateFileChildrens = (fileItemUploads: any[], itemChildren: any, fieldName: string) => {
        itemChildren[fieldName] = fileItemUploads;
    };

    // Table item management
    const addItem = (fieldItem: any) => {
        if (!state.itemChildrens[fieldItem.name]) {
            state.itemChildrens[fieldItem.name] = [];
        }
        state.itemChildrens[fieldItem.name].push({ ...initializeItem(fieldItem.childrens) });
    };

    const removeItem = (fieldItem: any, itemIndex: number) => {
        if (state.itemChildrens[fieldItem.name] && itemIndex > 0) {
            state.itemChildrens[fieldItem.name].splice(itemIndex, 1);
        }
    };

    // Validation schema
    const createValidationSchema = (field: any[]) => {
        let schemaForm = yup.object().shape({});

        // Main form validation
        const itemShape: { [key: string]: any } = {};

        field.forEach((item: any) => {
            if (item.type !== 'TABLE') {
                if (item.validation && item.disabled == false) {
                    switch (item.type) {
                        case 'FILEUPLOAD':
                            const validationRule = yup.array().required(`${item.validationMessages.required}`);
                            itemShape[item.name] = validationRule;
                            break;
                        case 'MULTISELECT':
                            if (!item.value || item.value?.length === 0) {
                                const validationRule = item.multiple
                                    ? yup.array().min(1, `${item.validationMessages.required}`).required(`${item.validationMessages.required}`)
                                    : yup.string().required(`${item.validationMessages.required}`);
                                itemShape[item.name] = validationRule;
                            }
                            break;
                        case 'OBJECTSYSTEM':
                        case 'USER':
                        case 'DEPARTMENT':
                            if (!item.value || item.value?.length === 0) {
                                const validationRule = item.multiple
                                    ? yup.array().min(1, `${item.validationMessages.required}`).required(`${item.validationMessages.required}`)
                                    : yup.object().required(`${item.validationMessages.required}`);
                                itemShape[item.name] = validationRule;
                            }
                            break;
                        default:
                            itemShape[item.name] = yup.mixed();
                    }
                }
            }
        });

        // Table children validation
        schemaForm = schemaForm.shape({
            itemChildrens: yup.array().of(
                yup.lazy((itemChildren: any) => {
                    const itemChildrenShape: { [key: string]: any } = {};

                    field.forEach((item: any) => {
                        if (item.type === 'TABLE') {
                            const field_childrens = converFormFields(item.childrens, true);
                            field_childrens.forEach((item_children: any) => {
                                if (item_children.validation && item_children.disabled == false) {
                                    switch (item_children.type) {
                                        case 'FILEUPLOAD':
                                            const validationRule = yup.array().required(`${item_children.validationMessages.required}`);
                                            itemChildrenShape[item_children.name] = validationRule;
                                            break;
                                        case 'MULTISELECT':
                                            if (!item_children.value || itemChildren[item_children.name]?.length === 0) {
                                                const validationRule = item_children.multiple
                                                    ? yup.array().min(1, `${item_children.validationMessages.required}`).required(`${item_children.validationMessages.required}`)
                                                    : yup.string().required(`${item_children.validationMessages.required}`);
                                                itemChildrenShape[item_children.name] = validationRule;
                                            }
                                            break;
                                        case 'OBJECTSYSTEM':
                                        case 'USER':
                                        case 'DEPARTMENT':
                                            if (!item_children.value || itemChildren[item_children.name]?.length === 0) {
                                                const validationRule = item_children.multiple
                                                    ? yup.array().min(1, `${item_children.validationMessages.required}`).required(`${item_children.validationMessages.required}`)
                                                    : yup.object().required(`${item_children.validationMessages.required}`);
                                                itemChildrenShape[item_children.name] = validationRule;
                                            }
                                            break;
                                        default:
                                            itemChildrenShape[item_children.name] = yup.mixed();
                                    }
                                }
                            });
                        }
                    });

                    return yup.object().shape(itemChildrenShape);
                })
            ),
            ...itemShape
        });

        return schemaForm;
    };

    return {
        state,
        sortedFormFields,
        converFormFields,
        transformObject,
        isJsonArrayOrObject,
        mappedTypeDataField,
        initializeItem,
        getOptionUsers,
        debouncedGetOptionUsers,
        getOptionDepartments,
        getOptionProcessScopes,
        debouncedGetOptionScopes,
        extractSumPlus,
        removeCommas,
        sumColumn,
        calculateFormula,
        formulaResults,
        formattedFormulaResults,
        formattedFormulaChildrenResults,
        updateFiles,
        updateFileChildrens,
        addItem,
        removeItem,
        createValidationSchema
    }
}
